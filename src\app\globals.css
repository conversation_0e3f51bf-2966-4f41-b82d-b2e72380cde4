@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter), ui-sans-serif, system-ui, sans-serif;
  --font-serif: var(--font-playfair), ui-serif, Georgia, serif;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Beauty Salon Color Palette */
  --background: oklch(0.99 0.005 15); /* Soft cream white */
  --foreground: oklch(0.25 0.02 15); /* Deep charcoal */
  --card: oklch(0.98 0.008 15); /* Warm white */
  --card-foreground: oklch(0.25 0.02 15);
  --popover: oklch(0.98 0.008 15);
  --popover-foreground: oklch(0.25 0.02 15);
  --primary: oklch(0.65 0.15 15); /* Blush pink */
  --primary-foreground: oklch(0.98 0.008 15);
  --secondary: oklch(0.92 0.02 25); /* Soft beige */
  --secondary-foreground: oklch(0.25 0.02 15);
  --muted: oklch(0.95 0.01 20); /* Light cream */
  --muted-foreground: oklch(0.55 0.02 15);
  --accent: oklch(0.75 0.12 340); /* Soft rose */
  --accent-foreground: oklch(0.98 0.008 15);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.88 0.02 20); /* Soft border */
  --input: oklch(0.95 0.01 20);
  --ring: oklch(0.65 0.15 15); /* Blush pink ring */
  --chart-1: oklch(0.65 0.15 15); /* Blush pink */
  --chart-2: oklch(0.75 0.12 340); /* Soft rose */
  --chart-3: oklch(0.85 0.08 30); /* Champagne */
  --chart-4: oklch(0.7 0.1 50); /* Warm gold */
  --chart-5: oklch(0.6 0.1 320); /* Mauve */
  --sidebar: oklch(0.98 0.008 15);
  --sidebar-foreground: oklch(0.25 0.02 15);
  --sidebar-primary: oklch(0.65 0.15 15);
  --sidebar-primary-foreground: oklch(0.98 0.008 15);
  --sidebar-accent: oklch(0.92 0.02 25);
  --sidebar-accent-foreground: oklch(0.25 0.02 15);
  --sidebar-border: oklch(0.88 0.02 20);
  --sidebar-ring: oklch(0.65 0.15 15);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Custom scrollbar for beauty salon aesthetic */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: oklch(0.95 0.01 20);
  }

  ::-webkit-scrollbar-thumb {
    background: oklch(0.65 0.15 15);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.6 0.15 15);
  }
}

@layer components {
  /* Beauty salon specific gradients */
  .gradient-rose {
    background: linear-gradient(135deg, oklch(0.75 0.12 340), oklch(0.65 0.15 15));
  }

  .gradient-cream {
    background: linear-gradient(135deg, oklch(0.98 0.008 15), oklch(0.92 0.02 25));
  }

  .gradient-gold {
    background: linear-gradient(135deg, oklch(0.85 0.08 30), oklch(0.7 0.1 50));
  }

  /* Elegant shadows */
  .shadow-elegant {
    box-shadow: 0 4px 20px -2px oklch(0.65 0.15 15 / 0.1);
  }

  .shadow-soft {
    box-shadow: 0 2px 10px -1px oklch(0.25 0.02 15 / 0.05);
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
