import { Button } from "@/components/ui/button";
import { ArrowRight, Sparkles } from "lucide-react";

interface HeroProps {
  title: string;
  subtitle?: string;
  description: string;
  primaryCTA?: string;
  secondaryCTA?: string;
  backgroundImage?: string;
  showSparkles?: boolean;
}

export default function Hero({
  title,
  subtitle,
  description,
  primaryCTA = "Book Appointment",
  secondaryCTA = "View Services",
  backgroundImage = "/api/placeholder/1920/800",
  showSparkles = true,
}: HeroProps) {
  return (
    <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${backgroundImage})`,
        }}
      >
        <div className="absolute inset-0 bg-black/40" />
      </div>

      {/* Decorative Elements */}
      {showSparkles && (
        <>
          <div className="absolute top-20 left-10 text-white/20">
            <Sparkles className="h-8 w-8 animate-pulse" />
          </div>
          <div className="absolute top-40 right-20 text-white/20">
            <Sparkles className="h-6 w-6 animate-pulse delay-1000" />
          </div>
          <div className="absolute bottom-32 left-20 text-white/20">
            <Sparkles className="h-10 w-10 animate-pulse delay-500" />
          </div>
        </>
      )}

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <div className="max-w-4xl mx-auto space-y-6 animate-fade-in">
          {subtitle && (
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
              <span className="text-sm font-medium">{subtitle}</span>
            </div>
          )}
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-playfair font-bold leading-tight">
            {title}
          </h1>
          
          <p className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
            {description}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6">
            <Button 
              size="lg" 
              className="gradient-rose text-white hover:opacity-90 group px-8 py-3"
            >
              {primaryCTA}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-foreground px-8 py-3"
            >
              {secondaryCTA}
            </Button>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center space-y-2">
          <div className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse" />
          </div>
          <span className="text-xs">Scroll</span>
        </div>
      </div>
    </section>
  );
}
