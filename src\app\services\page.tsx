import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Clock, 
  Star, 
  CheckCircle, 
  Sparkles,
  Heart,
  Palette,
  Scissors,
  Droplets,
  Hand
} from "lucide-react";

const serviceCategories = [
  {
    icon: Heart,
    title: "Bridal Services",
    description: "Complete bridal transformation packages for your special day",
    services: [
      {
        name: "Bridal Makeup",
        description: "Complete bridal look with premium products, including base, eyes, lips, and contouring. Includes trial session.",
        duration: "3-4 hours",
        price: "Rs. 15,000",
        features: ["Trial session included", "Premium products", "Long-lasting formula", "Touch-up kit provided"],
        image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop",
        popular: true,
      },
      {
        name: "Pre-Wedding Shoot Makeup",
        description: "Perfect makeup for your pre-wedding photography sessions with camera-friendly techniques.",
        duration: "2-3 hours",
        price: "Rs. 8,000",
        features: ["Camera-friendly makeup", "Multiple look options", "Touch-ups included", "Waterproof products"],
        image: "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&h=300&fit=crop",
      },
      {
        name: "Bridal Hair Styling",
        description: "Elegant bridal hairstyles including traditional and contemporary looks with accessories.",
        duration: "2-3 hours",
        price: "Rs. 5,000",
        features: ["Traditional & modern styles", "Hair accessories included", "Long-lasting hold", "Consultation included"],
        image: "https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=300&fit=crop",
      },
    ],
  },
  {
    icon: Palette,
    title: "Makeup Services",
    description: "Professional makeup for all occasions and events",
    services: [
      {
        name: "Party Makeup",
        description: "Glamorous makeup for parties, events, and special occasions with bold and elegant looks.",
        duration: "1.5-2 hours",
        price: "Rs. 3,500",
        features: ["Event-appropriate looks", "Long-lasting formula", "Photo-ready finish", "Complimentary touch-up"],
        image: "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=300&fit=crop",
      },
      {
        name: "Natural Day Makeup",
        description: "Subtle, natural makeup perfect for daily wear, office, or casual outings.",
        duration: "45 minutes",
        price: "Rs. 1,500",
        features: ["Natural finish", "Lightweight products", "Skin-friendly", "Quick application"],
        image: "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&h=300&fit=crop",
      },
      {
        name: "Fashion/Editorial Makeup",
        description: "Creative and artistic makeup for fashion shoots, editorials, and artistic projects.",
        duration: "2-3 hours",
        price: "Rs. 6,000",
        features: ["Creative concepts", "Artistic techniques", "Photo shoot ready", "Unique looks"],
        image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop",
      },
    ],
  },
  {
    icon: Scissors,
    title: "Hair Services",
    description: "Complete hair care and styling solutions",
    services: [
      {
        name: "Hair Cut & Styling",
        description: "Professional hair cutting and styling services for all hair types and lengths.",
        duration: "1-2 hours",
        price: "Rs. 1,200",
        features: ["Consultation included", "All hair types", "Modern techniques", "Styling tips"],
        image: "https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=300&fit=crop",
      },
      {
        name: "Hair Coloring",
        description: "Professional hair coloring services including highlights, lowlights, and full color treatments.",
        duration: "2-4 hours",
        price: "Rs. 4,500",
        features: ["Premium color products", "Color consultation", "Damage protection", "Aftercare guidance"],
        image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop",
      },
      {
        name: "Hair Treatment",
        description: "Deep conditioning and repair treatments for damaged, dry, or chemically treated hair.",
        duration: "1.5 hours",
        price: "Rs. 2,500",
        features: ["Deep conditioning", "Repair treatment", "Nourishing masks", "Scalp massage"],
        image: "https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=300&fit=crop",
      },
    ],
  },
  {
    icon: Droplets,
    title: "Skincare Services",
    description: "Advanced skincare treatments for healthy, glowing skin",
    services: [
      {
        name: "Classic Facial",
        description: "Deep cleansing facial with extraction, massage, and moisturizing for all skin types.",
        duration: "1 hour",
        price: "Rs. 2,000",
        features: ["Deep cleansing", "Extraction", "Face massage", "Moisturizing mask"],
        image: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
      },
      {
        name: "Anti-Aging Facial",
        description: "Specialized facial treatment targeting fine lines, wrinkles, and signs of aging.",
        duration: "1.5 hours",
        price: "Rs. 3,500",
        features: ["Anti-aging serums", "Collagen boost", "Firming treatment", "Age spot reduction"],
        image: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
      },
      {
        name: "Acne Treatment Facial",
        description: "Targeted treatment for acne-prone skin with deep cleansing and healing properties.",
        duration: "1.5 hours",
        price: "Rs. 2,800",
        features: ["Acne-fighting ingredients", "Deep pore cleansing", "Healing treatment", "Prevention tips"],
        image: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
      },
    ],
  },
  {
    icon: Hand,
    title: "Nail Services",
    description: "Professional nail care and artistic nail designs",
    services: [
      {
        name: "Manicure & Pedicure",
        description: "Complete nail care including shaping, cuticle care, and polish application.",
        duration: "1.5 hours",
        price: "Rs. 1,200",
        features: ["Nail shaping", "Cuticle care", "Hand/foot massage", "Polish application"],
        image: "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
      },
      {
        name: "Nail Art",
        description: "Creative nail designs and artistic patterns for special occasions and personal style.",
        duration: "1-2 hours",
        price: "Rs. 1,800",
        features: ["Custom designs", "Artistic patterns", "Quality materials", "Long-lasting finish"],
        image: "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
      },
      {
        name: "Gel Nail Extensions",
        description: "Professional gel nail extensions for length and strength with natural-looking results.",
        duration: "2-3 hours",
        price: "Rs. 2,500",
        features: ["Natural look", "Strong & durable", "Custom length", "Professional application"],
        image: "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
      },
    ],
  },
];

export default function Services() {
  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <Hero
        title="Our Beauty Services"
        subtitle="Professional Beauty Solutions"
        description="Discover our comprehensive range of beauty services designed to enhance your natural beauty and boost your confidence."
        backgroundImage="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=1920&h=800&fit=crop"
        primaryCTA="Book Service"
        secondaryCTA="View Pricing"
      />

      {/* Services Overview */}
      <Section>
        <SectionHeader
          subtitle="What We Offer"
          title="Complete Beauty Solutions"
          description="From bridal transformations to everyday beauty needs, we offer a full range of professional services."
        />
        
        <div className="grid gap-12">
          {serviceCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-8">
              {/* Category Header */}
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-rose rounded-full flex items-center justify-center">
                  <category.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-playfair font-bold text-foreground">
                    {category.title}
                  </h3>
                  <p className="text-muted-foreground">{category.description}</p>
                </div>
              </div>
              
              {/* Services Grid */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {category.services.map((service, serviceIndex) => (
                  <Card key={serviceIndex} className="overflow-hidden hover:shadow-elegant transition-all duration-300 hover:-translate-y-1">
                    <div className="relative">
                      {service.popular && (
                        <Badge className="absolute top-3 left-3 z-10 bg-gradient-rose text-white">
                          Popular
                        </Badge>
                      )}
                      <div 
                        className="h-48 bg-cover bg-center"
                        style={{ backgroundImage: `url(${service.image})` }}
                      >
                        <div className="absolute inset-0 bg-black/20" />
                      </div>
                      <div className="absolute bottom-3 right-3 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                        <span className="text-sm font-semibold text-foreground">{service.price}</span>
                      </div>
                    </div>
                    
                    <CardContent className="p-6 space-y-4">
                      <div className="space-y-2">
                        <h4 className="text-xl font-playfair font-semibold text-foreground">
                          {service.name}
                        </h4>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          {service.description}
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>{service.duration}</span>
                      </div>
                      
                      <div className="space-y-2">
                        <h5 className="font-medium text-foreground">Includes:</h5>
                        <ul className="space-y-1">
                          {service.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <CheckCircle className="h-3 w-3 text-primary flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <Button className="w-full gradient-rose text-white hover:opacity-90">
                        Book This Service
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Service Packages */}
      <Section background="gradient">
        <SectionHeader
          subtitle="Special Packages"
          title="Complete Beauty Packages"
          description="Save more with our specially curated beauty packages designed for different occasions."
        />

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="p-6 hover:shadow-elegant transition-shadow border-2 border-primary/20">
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-playfair font-bold text-foreground">Bridal Package</h3>
                <p className="text-muted-foreground text-sm">Complete bridal transformation</p>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-primary">Rs. 25,000</div>
                <div className="text-sm text-muted-foreground line-through">Rs. 30,000</div>
              </div>

              <ul className="space-y-2">
                {[
                  "Bridal makeup with trial",
                  "Bridal hair styling",
                  "Pre-wedding shoot makeup",
                  "Manicure & pedicure",
                  "Facial treatment"
                ].map((item, index) => (
                  <li key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>

              <Button className="w-full gradient-rose text-white">
                Book Package
              </Button>
            </CardContent>
          </Card>

          <Card className="p-6 hover:shadow-elegant transition-shadow">
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-playfair font-bold text-foreground">Party Package</h3>
                <p className="text-muted-foreground text-sm">Perfect for special events</p>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-primary">Rs. 8,000</div>
                <div className="text-sm text-muted-foreground line-through">Rs. 10,000</div>
              </div>

              <ul className="space-y-2">
                {[
                  "Party makeup",
                  "Hair styling",
                  "Manicure",
                  "Basic facial",
                  "Touch-up kit"
                ].map((item, index) => (
                  <li key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>

              <Button className="w-full gradient-rose text-white">
                Book Package
              </Button>
            </CardContent>
          </Card>

          <Card className="p-6 hover:shadow-elegant transition-shadow">
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto mb-4">
                  <Droplets className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-playfair font-bold text-foreground">Glow Package</h3>
                <p className="text-muted-foreground text-sm">Complete skincare treatment</p>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-primary">Rs. 6,500</div>
                <div className="text-sm text-muted-foreground line-through">Rs. 8,000</div>
              </div>

              <ul className="space-y-2">
                {[
                  "Anti-aging facial",
                  "Hair treatment",
                  "Manicure & pedicure",
                  "Natural day makeup",
                  "Skincare consultation"
                ].map((item, index) => (
                  <li key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>

              <Button className="w-full gradient-rose text-white">
                Book Package
              </Button>
            </CardContent>
          </Card>
        </div>
      </Section>

      {/* CTA Section */}
      <Section>
        <div className="text-center space-y-6">
          <h2 className="text-3xl md:text-4xl font-playfair font-bold text-foreground">
            Ready to Book Your Service?
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Transform your look with our professional beauty services. Book your appointment today
            and experience the difference our expert team can make.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="gradient-rose text-white">
              Book Appointment
            </Button>
            <Button size="lg" variant="outline">
              Call Us: +977-9800000000
            </Button>
          </div>
        </div>
      </Section>

      <Footer />
    </div>
  );
}
