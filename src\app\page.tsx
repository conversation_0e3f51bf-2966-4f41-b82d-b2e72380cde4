import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import ServiceCard from "@/components/ServiceCard";
import TestimonialCard from "@/components/TestimonialCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>rk<PERSON>,
  Award,
  Users,
  Heart,
  Shield,
  Star,
  ArrowRight,
  CheckCircle
} from "lucide-react";
import Link from "next/link";

const services = [
  {
    title: "Bridal Makeup",
    description: "Complete bridal transformation with premium products and expert techniques for your special day.",
    image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop",
    duration: "3-4 hours",
    price: "Rs. 15,000",
    rating: 4.9,
    popular: true,
  },
  {
    title: "Hair Styling",
    description: "Professional hair cutting, coloring, and styling services for all occasions.",
    image: "https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=300&fit=crop",
    duration: "2-3 hours",
    price: "Rs. 3,000",
    rating: 4.8,
  },
  {
    title: "Facial Treatment",
    description: "Rejuvenating facial treatments for glowing, healthy skin using premium skincare products.",
    image: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
    duration: "1-2 hours",
    price: "Rs. 2,500",
    rating: 4.7,
  },
  {
    title: "Nail Art",
    description: "Creative nail designs and professional manicure/pedicure services.",
    image: "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
    duration: "1 hour",
    price: "Rs. 1,500",
    rating: 4.6,
  },
];

const testimonials = [
  {
    name: "Priya Sharma",
    role: "Bride",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Bella Beauty Studio made my wedding day absolutely perfect! The bridal makeup was flawless and lasted all day.",
    service: "Bridal Makeup",
  },
  {
    name: "Anita Rai",
    role: "Regular Client",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "The best salon in Biratnagar! Professional service, friendly staff, and amazing results every time.",
    service: "Hair Styling",
  },
  {
    name: "Sunita Thapa",
    role: "Beauty Enthusiast",
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Their facial treatments are incredible! My skin has never looked better. Highly recommend!",
    service: "Facial Treatment",
  },
];

const features = [
  {
    icon: Award,
    title: "Professional Artists",
    description: "Certified makeup artists and stylists with years of experience",
  },
  {
    icon: Sparkles,
    title: "Premium Products",
    description: "We use only high-quality, branded cosmetics and skincare products",
  },
  {
    icon: Shield,
    title: "Hygienic Setup",
    description: "Maintaining the highest standards of cleanliness and hygiene",
  },
  {
    icon: Heart,
    title: "Personalized Care",
    description: "Customized beauty solutions tailored to your unique needs",
  },
];

const partners = [
  { name: "MAC", logo: "https://via.placeholder.com/120x60/000000/FFFFFF?text=MAC" },
  { name: "L'Oréal", logo: "https://via.placeholder.com/120x60/000000/FFFFFF?text=L'Oreal" },
  { name: "Lakmé", logo: "https://via.placeholder.com/120x60/000000/FFFFFF?text=Lakme" },
  { name: "Maybelline", logo: "https://via.placeholder.com/120x60/000000/FFFFFF?text=Maybelline" },
  { name: "NARS", logo: "https://via.placeholder.com/120x60/000000/FFFFFF?text=NARS" },
];

export default function Home() {
  return (
    <div className="min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <Hero
        title="Enhance Your Natural Beauty"
        subtitle="Premium Beauty Studio"
        description="Transform your look with our expert makeup artists and stylists. From bridal makeup to everyday beauty, we bring out the best in you."
        backgroundImage="https://images.unsplash.com/photo-**********-138dadb4c035?w=1920&h=800&fit=crop"
      />

      {/* About Preview */}
      <Section background="muted">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              About Bella Beauty Studio
            </div>
            <h2 className="text-3xl md:text-4xl font-playfair font-bold text-foreground">
              Where Beauty Meets Excellence
            </h2>
            <p className="text-muted-foreground leading-relaxed">
              Located in the heart of Biratnagar, Bella Beauty Studio has been transforming
              lives through beauty for over 5 years. Our team of certified professionals
              specializes in bridal makeup, hair styling, and premium beauty treatments.
            </p>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-background rounded-lg">
                <div className="text-2xl font-bold text-primary">500+</div>
                <div className="text-sm text-muted-foreground">Happy Brides</div>
              </div>
              <div className="text-center p-4 bg-background rounded-lg">
                <div className="text-2xl font-bold text-primary">1000+</div>
                <div className="text-sm text-muted-foreground">Satisfied Clients</div>
              </div>
            </div>
            <Link href="/about">
              <Button className="gradient-rose text-white group">
                Learn More About Us
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </div>
          <div className="relative">
            <div
              className="aspect-square rounded-2xl bg-cover bg-center shadow-elegant"
              style={{
                backgroundImage: "url('https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600&h=600&fit=crop')"
              }}
            />
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-gradient-rose rounded-full flex items-center justify-center">
              <Sparkles className="h-12 w-12 text-white" />
            </div>
          </div>
        </div>
      </Section>

      {/* Services Overview */}
      <Section>
        <SectionHeader
          subtitle="Our Services"
          title="Beauty Services That Transform"
          description="Discover our range of professional beauty services designed to enhance your natural beauty and boost your confidence."
        />
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {services.map((service, index) => (
            <ServiceCard key={index} {...service} />
          ))}
        </div>
        <div className="text-center mt-12">
          <Link href="/services">
            <Button size="lg" variant="outline" className="group">
              View All Services
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </Section>

      {/* Gallery Preview */}
      <Section background="gradient">
        <SectionHeader
          subtitle="Our Work"
          title="Beauty Gallery"
          description="Take a look at our stunning transformations and see the artistry of our professional team."
        />
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop",
          ].map((image, index) => (
            <div
              key={index}
              className="aspect-square rounded-xl bg-cover bg-center hover:scale-105 transition-transform duration-300 cursor-pointer shadow-soft"
              style={{ backgroundImage: `url(${image})` }}
            />
          ))}
        </div>
        <div className="text-center mt-12">
          <Link href="/gallery">
            <Button size="lg" className="gradient-rose text-white group">
              View Full Gallery
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </Section>

      {/* Why Choose Us */}
      <Section>
        <SectionHeader
          subtitle="Why Choose Us"
          title="Excellence in Every Detail"
          description="We're committed to providing the highest quality beauty services with attention to every detail."
        />
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="text-center p-6 hover:shadow-elegant transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-playfair font-semibold">{feature.title}</h3>
                <p className="text-muted-foreground text-sm">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Testimonials */}
      <Section background="muted">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Clients Say"
          description="Don't just take our word for it. Here's what our satisfied clients have to say about their experience."
        />
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} {...testimonial} />
          ))}
        </div>
        <div className="text-center mt-12">
          <Link href="/reviews">
            <Button size="lg" variant="outline" className="group">
              Read More Reviews
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </Section>

      {/* Trusted Partners */}
      <Section>
        <SectionHeader
          subtitle="Trusted Partners"
          title="Premium Brand Partners"
          description="We work with the world's leading beauty brands to ensure the highest quality products and results."
        />
        <div className="flex flex-wrap justify-center items-center gap-8 opacity-60 hover:opacity-100 transition-opacity">
          {partners.map((partner, index) => (
            <div key={index} className="grayscale hover:grayscale-0 transition-all">
              <img
                src={partner.logo}
                alt={partner.name}
                className="h-12 object-contain"
              />
            </div>
          ))}
        </div>
      </Section>

      <Footer />
    </div>
  );
}
