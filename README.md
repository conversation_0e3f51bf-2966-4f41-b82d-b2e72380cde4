# Bella Beauty Studio - Premium Beauty Salon Website

A fully responsive, modern beauty salon website built with Next.js, Tailwind CSS, and ShadCN UI components. This static website showcases a premium beauty salon in Biratnagar, Nepal, featuring elegant design, smooth animations, and comprehensive service information.

## 🌟 Features

### Design & UI
- **Elegant & Feminine Design**: Soft color palette with blush pink, beige, and cream tones
- **Fully Responsive**: Mobile-first design that works perfectly on all devices
- **Modern Typography**: Beautiful font combinations with Playfair Display and Inter
- **Smooth Animations**: CSS animations and hover effects for enhanced user experience
- **Professional Layout**: Clean, organized sections with consistent spacing

### Pages & Functionality
- **Home Page**: Hero section, services overview, gallery preview, testimonials, and trusted partners
- **About Page**: Founder's story, team introduction, studio history, mission & vision
- **Services Page**: Detailed service listings with pricing, duration, and features
- **Pricing Page**: Transparent pricing tables and special packages
- **Gallery Page**: Interactive image gallery with lightbox and category filtering
- **Reviews Page**: Client testimonials with ratings and review statistics
- **Contact Page**: Contact form, location info, FAQ section, and social media links

### Technical Features
- **Next.js 15**: Latest version with App Router and TypeScript
- **Tailwind CSS v4**: Modern utility-first CSS framework
- **ShadCN UI**: High-quality, accessible UI components
- **Responsive Images**: Optimized images with proper aspect ratios
- **SEO Optimized**: Proper meta tags, structured data, and semantic HTML
- **Performance Optimized**: Fast loading times and smooth interactions

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd salon
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
salon/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── about/             # About page
│   │   ├── contact/           # Contact page
│   │   ├── gallery/           # Gallery page
│   │   ├── pricing/           # Pricing page
│   │   ├── reviews/           # Reviews page
│   │   ├── services/          # Services page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # Reusable components
│   │   ├── ui/               # ShadCN UI components
│   │   ├── Footer.tsx        # Site footer
│   │   ├── Hero.tsx          # Hero section component
│   │   ├── Navbar.tsx        # Navigation component
│   │   ├── Section.tsx       # Section wrapper component
│   │   ├── ServiceCard.tsx   # Service card component
│   │   └── TestimonialCard.tsx # Testimonial component
│   └── lib/
│       └── utils.ts          # Utility functions
├── public/                   # Static assets
├── components.json          # ShadCN UI configuration
├── next.config.ts          # Next.js configuration
├── tailwind.config.ts      # Tailwind CSS configuration
└── package.json           # Dependencies and scripts
```

## 🎨 Design System

### Color Palette
- **Primary**: Blush Pink (`oklch(0.65 0.15 15)`)
- **Secondary**: Soft Beige (`oklch(0.92 0.02 25)`)
- **Accent**: Soft Rose (`oklch(0.75 0.12 340)`)
- **Background**: Soft Cream White (`oklch(0.99 0.005 15)`)
- **Foreground**: Deep Charcoal (`oklch(0.25 0.02 15)`)

### Typography
- **Headings**: Playfair Display (serif)
- **Body Text**: Inter (sans-serif)
- **UI Elements**: Inter (sans-serif)

### Components
- **Cards**: Soft shadows with hover effects
- **Buttons**: Gradient backgrounds with smooth transitions
- **Forms**: Clean inputs with focus states
- **Navigation**: Sticky header with scroll effects

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

All components adapt seamlessly across different screen sizes with mobile-first approach.

## 🛠️ Technologies Used

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: ShadCN UI
- **Icons**: Lucide React
- **Animations**: CSS transitions and keyframes
- **Fonts**: Google Fonts (Inter, Playfair Display)

## 📄 Pages Overview

### Home Page
- Hero section with call-to-action
- About preview with statistics
- Services overview grid
- Gallery preview
- Why choose us features
- Client testimonials
- Trusted brand partners

### About Page
- Founder's story and message
- Mission and vision statements
- Team member profiles
- Studio history timeline
- Core values and principles

### Services Page
- Categorized service listings
- Detailed service descriptions
- Pricing and duration info
- Special service packages
- Booking call-to-actions

### Pricing Page
- Transparent pricing tables
- Service packages with savings
- Payment information
- Booking process steps

### Gallery Page
- Interactive image gallery
- Category filtering
- Lightbox with navigation
- Before/after showcases

### Reviews Page
- Client testimonials
- Rating statistics
- Review highlights
- Social proof elements

### Contact Page
- Contact information
- Booking form
- Location details
- FAQ section
- Social media links

## 🎯 Business Information

**Bella Beauty Studio**
- Location: Biratnagar, Nepal
- Services: Bridal Makeup, Hair Styling, Facial Treatments, Nail Art
- Specialization: Bridal transformations and premium beauty services
- Target Audience: Brides, beauty enthusiasts, special occasion clients

## 📞 Contact Information

- **Phone**: +977-9800000000
- **Email**: <EMAIL>
- **Address**: Main Road, Biratnagar, Morang, Nepal
- **Hours**: Mon-Sat 9AM-7PM, Sun 10AM-5PM

---

**Built with ❤️ for the beauty industry in Nepal**
