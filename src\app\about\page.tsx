import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Award, 
  Users, 
  Heart, 
  Target, 
  Eye, 
  Star,
  Calendar,
  MapPin,
  Phone,
  Mail
} from "lucide-react";

const teamMembers = [
  {
    name: "<PERSON><PERSON>",
    role: "Founder & Lead Makeup Artist",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",
    experience: "8+ years",
    specialization: "Bridal Makeup",
    description: "Certified makeup artist with expertise in bridal and fashion makeup. Passionate about enhancing natural beauty.",
  },
  {
    name: "<PERSON> <PERSON>",
    role: "Senior Hair Stylist",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face",
    experience: "6+ years",
    specialization: "Hair Styling & Coloring",
    description: "Expert in modern hair styling techniques and color treatments. Creates stunning looks for every occasion.",
  },
  {
    name: "Priya Thapa",
    role: "Skincare Specialist",
    image: "https://images.unsplash.com/photo-**********-94ddf0286df2?w=300&h=300&fit=crop&crop=face",
    experience: "5+ years",
    specialization: "Facial Treatments",
    description: "Specialized in advanced skincare treatments and facial therapies for healthy, glowing skin.",
  },
];

const milestones = [
  {
    year: "2019",
    title: "Studio Founded",
    description: "Bella Beauty Studio opened its doors in Biratnagar with a vision to transform beauty standards.",
  },
  {
    year: "2020",
    title: "Team Expansion",
    description: "Added certified professionals to our team, expanding our service offerings.",
  },
  {
    year: "2021",
    title: "500+ Brides",
    description: "Celebrated our milestone of serving over 500 beautiful brides.",
  },
  {
    year: "2022",
    title: "Premium Partnerships",
    description: "Partnered with international beauty brands to offer premium products.",
  },
  {
    year: "2023",
    title: "Award Recognition",
    description: "Recognized as 'Best Beauty Salon' in Biratnagar by local beauty awards.",
  },
  {
    year: "2024",
    title: "1000+ Clients",
    description: "Reached the milestone of serving over 1000 satisfied clients.",
  },
];

const values = [
  {
    icon: Heart,
    title: "Passion",
    description: "We are passionate about beauty and dedicated to making every client feel special and confident.",
  },
  {
    icon: Award,
    title: "Excellence",
    description: "We strive for excellence in every service, using premium products and advanced techniques.",
  },
  {
    icon: Users,
    title: "Community",
    description: "We believe in building strong relationships with our clients and contributing to our community.",
  },
  {
    icon: Target,
    title: "Innovation",
    description: "We continuously learn and adopt new techniques to stay at the forefront of beauty trends.",
  },
];

export default function About() {
  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <Hero
        title="Our Story"
        subtitle="About Bella Beauty Studio"
        description="Discover the passion, expertise, and dedication behind Biratnagar's premier beauty destination."
        backgroundImage="https://images.unsplash.com/photo-1560066984-138dadb4c035?w=1920&h=800&fit=crop"
        primaryCTA="Meet Our Team"
        secondaryCTA="Our Services"
      />

      {/* Founder's Story */}
      <Section>
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              Founder's Message
            </div>
            <h2 className="text-3xl md:text-4xl font-playfair font-bold text-foreground">
              A Dream Born from Passion
            </h2>
            <div className="space-y-4 text-muted-foreground leading-relaxed">
              <p>
                "Beauty has always been my passion. When I started Bella Beauty Studio in 2019, 
                my vision was simple yet profound - to create a space where every woman could 
                discover and enhance her natural beauty."
              </p>
              <p>
                "With over 8 years of experience in the beauty industry, I've had the privilege 
                of being part of countless special moments - from bridal transformations to 
                everyday confidence boosts. Each client's smile is what drives our commitment 
                to excellence."
              </p>
              <p>
                "Today, Bella Beauty Studio stands as a testament to our dedication, serving 
                over 1000 satisfied clients and continuing to set new standards in beauty 
                services in Biratnagar."
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full bg-cover bg-center" 
                   style={{backgroundImage: "url('https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face')"}} />
              <div>
                <div className="font-semibold text-foreground">Sita Sharma</div>
                <div className="text-sm text-muted-foreground">Founder & Lead Makeup Artist</div>
              </div>
            </div>
          </div>
          <div className="relative">
            <div 
              className="aspect-[4/5] rounded-2xl bg-cover bg-center shadow-elegant"
              style={{
                backgroundImage: "url('https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600&h=750&fit=crop')"
              }}
            />
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-gradient-rose rounded-full flex items-center justify-center">
              <Heart className="h-12 w-12 text-white" />
            </div>
          </div>
        </div>
      </Section>

      {/* Mission & Vision */}
      <Section background="gradient">
        <div className="grid lg:grid-cols-2 gap-12">
          <Card className="p-8 hover:shadow-elegant transition-shadow">
            <CardContent className="space-y-6">
              <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-playfair font-bold text-foreground">Our Mission</h3>
              <p className="text-muted-foreground leading-relaxed">
                To empower every individual by enhancing their natural beauty through professional, 
                personalized beauty services. We are committed to using premium products, 
                maintaining the highest hygiene standards, and creating an experience that 
                boosts confidence and celebrates uniqueness.
              </p>
            </CardContent>
          </Card>
          
          <Card className="p-8 hover:shadow-elegant transition-shadow">
            <CardContent className="space-y-6">
              <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center">
                <Eye className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-playfair font-bold text-foreground">Our Vision</h3>
              <p className="text-muted-foreground leading-relaxed">
                To become Nepal's most trusted and innovative beauty destination, setting new 
                standards in beauty services while fostering a community where beauty, 
                confidence, and self-expression flourish. We envision expanding our reach 
                while maintaining our commitment to excellence and personalized care.
              </p>
            </CardContent>
          </Card>
        </div>
      </Section>

      {/* Team Introduction */}
      <Section>
        <SectionHeader
          subtitle="Our Team"
          title="Meet Our Expert Professionals"
          description="Our certified team of beauty professionals brings years of experience and passion to every service."
        />
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <Card key={index} className="overflow-hidden hover:shadow-elegant transition-shadow">
              <div
                className="h-64 bg-cover bg-center"
                style={{ backgroundImage: `url(${member.image})` }}
              />
              <CardContent className="p-6 space-y-4">
                <div>
                  <h3 className="text-xl font-playfair font-semibold text-foreground">
                    {member.name}
                  </h3>
                  <p className="text-primary font-medium">{member.role}</p>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <Badge variant="secondary">{member.experience}</Badge>
                  <span className="text-muted-foreground">{member.specialization}</span>
                </div>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {member.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Studio History */}
      <Section background="muted">
        <SectionHeader
          subtitle="Our Journey"
          title="Studio History & Milestones"
          description="From humble beginnings to becoming Biratnagar's premier beauty destination."
        />
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-rose hidden lg:block" />

          <div className="space-y-12">
            {milestones.map((milestone, index) => (
              <div key={index} className={`flex items-center ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'}`}>
                <div className="flex-1 lg:pr-8">
                  <Card className={`p-6 hover:shadow-elegant transition-shadow ${index % 2 === 0 ? 'lg:text-right' : ''}`}>
                    <CardContent className="space-y-3">
                      <div className="inline-flex items-center px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                        {milestone.year}
                      </div>
                      <h3 className="text-xl font-playfair font-semibold text-foreground">
                        {milestone.title}
                      </h3>
                      <p className="text-muted-foreground">
                        {milestone.description}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Timeline dot */}
                <div className="hidden lg:flex w-4 h-4 bg-gradient-rose rounded-full border-4 border-background z-10" />

                <div className="flex-1 lg:pl-8" />
              </div>
            ))}
          </div>
        </div>
      </Section>

      {/* Values */}
      <Section>
        <SectionHeader
          subtitle="Our Values"
          title="What We Stand For"
          description="The core principles that guide everything we do at Bella Beauty Studio."
        />
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => (
            <Card key={index} className="text-center p-6 hover:shadow-elegant transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto">
                  <value.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-playfair font-semibold">{value.title}</h3>
                <p className="text-muted-foreground text-sm">{value.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Contact CTA */}
      <Section background="gradient">
        <div className="text-center space-y-6">
          <h2 className="text-3xl md:text-4xl font-playfair font-bold text-foreground">
            Ready to Experience the Difference?
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Join thousands of satisfied clients who have trusted us with their beauty needs.
            Book your appointment today and let us help you look and feel your best.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="gradient-rose text-white">
              Book Appointment
            </Button>
            <Button size="lg" variant="outline">
              Contact Us
            </Button>
          </div>
        </div>
      </Section>

      <Footer />
    </div>
  );
}
