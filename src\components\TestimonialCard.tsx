import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Quote } from "lucide-react";

interface TestimonialCardProps {
  name: string;
  role?: string;
  image?: string;
  rating: number;
  testimonial: string;
  service?: string;
}

export default function TestimonialCard({
  name,
  role,
  image,
  rating,
  testimonial,
  service,
}: TestimonialCardProps) {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();

  return (
    <Card className="h-full bg-gradient-cream border-border hover:shadow-elegant transition-all duration-300">
      <CardContent className="p-6 space-y-4">
        {/* Quote Icon */}
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-gradient-rose rounded-full flex items-center justify-center">
            <Quote className="h-6 w-6 text-white" />
          </div>
        </div>
        
        {/* Rating */}
        <div className="flex justify-center space-x-1">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${
                i < rating
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-gray-300"
              }`}
            />
          ))}
        </div>
        
        {/* Testimonial */}
        <blockquote className="text-center text-muted-foreground italic leading-relaxed">
          "{testimonial}"
        </blockquote>
        
        {/* Service */}
        {service && (
          <div className="text-center">
            <span className="inline-block px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">
              {service}
            </span>
          </div>
        )}
        
        {/* Author */}
        <div className="flex flex-col items-center space-y-2 pt-4 border-t border-border">
          <Avatar className="h-12 w-12">
            <AvatarImage src={image} alt={name} />
            <AvatarFallback className="bg-gradient-rose text-white font-semibold">
              {initials}
            </AvatarFallback>
          </Avatar>
          
          <div className="text-center">
            <h4 className="font-semibold text-foreground">{name}</h4>
            {role && (
              <p className="text-sm text-muted-foreground">{role}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
