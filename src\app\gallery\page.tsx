"use client";

import { useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Heart, 
  Palette, 
  Scissors, 
  Droplets, 
  Camera,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

const galleryCategories = [
  { id: "all", name: "All", icon: Camera },
  { id: "bridal", name: "Bridal", icon: Heart },
  { id: "makeup", name: "Makeup", icon: Palette },
  { id: "hair", name: "Hair", icon: Scissors },
  { id: "skincare", name: "Skincare", icon: Droplets },
];

const galleryImages = [
  {
    id: 1,
    src: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600&h=600&fit=crop",
    category: "bridal",
    title: "Bridal Makeup",
    description: "Traditional bridal look with modern touch",
  },
  {
    id: 2,
    src: "https://images.unsplash.com/photo-**********-138dadb4c035?w=600&h=600&fit=crop",
    category: "hair",
    title: "Hair Styling",
    description: "Elegant updo for special occasions",
  },
  {
    id: 3,
    src: "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=600&h=600&fit=crop",
    category: "makeup",
    title: "Party Makeup",
    description: "Glamorous evening look",
  },
  {
    id: 4,
    src: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600&h=600&fit=crop",
    category: "skincare",
    title: "Facial Treatment",
    description: "Glowing skin after facial treatment",
  },
  {
    id: 5,
    src: "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=600&h=600&fit=crop",
    category: "makeup",
    title: "Natural Makeup",
    description: "Fresh and natural everyday look",
  },
  {
    id: 6,
    src: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600&h=600&fit=crop",
    category: "bridal",
    title: "Wedding Day",
    description: "Complete bridal transformation",
  },
  {
    id: 7,
    src: "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=600&h=600&fit=crop",
    category: "makeup",
    title: "Nail Art",
    description: "Creative nail designs",
  },
  {
    id: 8,
    src: "https://images.unsplash.com/photo-**********-138dadb4c035?w=600&h=600&fit=crop",
    category: "hair",
    title: "Hair Color",
    description: "Beautiful hair coloring results",
  },
  {
    id: 9,
    src: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600&h=600&fit=crop",
    category: "bridal",
    title: "Pre-Wedding",
    description: "Pre-wedding photoshoot makeup",
  },
  {
    id: 10,
    src: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600&h=600&fit=crop",
    category: "skincare",
    title: "Anti-Aging Treatment",
    description: "Rejuvenating facial results",
  },
  {
    id: 11,
    src: "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=600&h=600&fit=crop",
    category: "makeup",
    title: "Editorial Makeup",
    description: "Fashion photoshoot makeup",
  },
  {
    id: 12,
    src: "https://images.unsplash.com/photo-**********-138dadb4c035?w=600&h=600&fit=crop",
    category: "hair",
    title: "Hair Treatment",
    description: "Healthy, shiny hair after treatment",
  },
];

export default function Gallery() {
  const [activeCategory, setActiveCategory] = useState("all");
  const [selectedImage, setSelectedImage] = useState<typeof galleryImages[0] | null>(null);

  const filteredImages = activeCategory === "all" 
    ? galleryImages 
    : galleryImages.filter(img => img.category === activeCategory);

  const openLightbox = (image: typeof galleryImages[0]) => {
    setSelectedImage(image);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (!selectedImage) return;
    
    const currentIndex = filteredImages.findIndex(img => img.id === selectedImage.id);
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : filteredImages.length - 1;
    } else {
      newIndex = currentIndex < filteredImages.length - 1 ? currentIndex + 1 : 0;
    }
    
    setSelectedImage(filteredImages[newIndex]);
  };

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <Hero
        title="Our Gallery"
        subtitle="Beauty Transformations"
        description="Explore our stunning portfolio of beauty transformations, showcasing the artistry and expertise of our professional team."
        backgroundImage="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=1920&h=800&fit=crop"
        primaryCTA="Book Service"
        secondaryCTA="View Services"
      />

      {/* Gallery Section */}
      <Section>
        <SectionHeader
          subtitle="Our Work"
          title="Beauty Portfolio"
          description="Browse through our collection of beautiful transformations and see the quality of our work."
        />
        
        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {galleryCategories.map((category) => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? "default" : "outline"}
              className={`flex items-center space-x-2 ${
                activeCategory === category.id 
                  ? "gradient-rose text-white" 
                  : "hover:bg-primary hover:text-primary-foreground"
              }`}
              onClick={() => setActiveCategory(category.id)}
            >
              <category.icon className="h-4 w-4" />
              <span>{category.name}</span>
            </Button>
          ))}
        </div>
        
        {/* Image Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredImages.map((image) => (
            <div
              key={image.id}
              className="group relative aspect-square overflow-hidden rounded-xl cursor-pointer hover:shadow-elegant transition-all duration-300 hover:-translate-y-1"
              onClick={() => openLightbox(image)}
            >
              <div
                className="w-full h-full bg-cover bg-center transition-transform duration-300 group-hover:scale-110"
                style={{ backgroundImage: `url(${image.src})` }}
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300" />
              <div className="absolute inset-0 flex items-end p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="text-white">
                  <h3 className="font-semibold text-lg">{image.title}</h3>
                  <p className="text-sm text-white/80">{image.description}</p>
                </div>
              </div>
              <Badge className="absolute top-3 left-3 bg-gradient-rose text-white capitalize">
                {image.category}
              </Badge>
            </div>
          ))}
        </div>
        
        {filteredImages.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No images found in this category.</p>
          </div>
        )}
      </Section>

      {/* Lightbox Modal */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            {/* Close Button */}
            <button
              onClick={closeLightbox}
              className="absolute top-4 right-4 z-10 w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
            
            {/* Navigation Buttons */}
            <button
              onClick={() => navigateImage('prev')}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>
            
            <button
              onClick={() => navigateImage('next')}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <ChevronRight className="h-6 w-6" />
            </button>
            
            {/* Image */}
            <img
              src={selectedImage.src}
              alt={selectedImage.title}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            
            {/* Image Info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white">
              <h3 className="text-xl font-semibold mb-1">{selectedImage.title}</h3>
              <p className="text-white/80">{selectedImage.description}</p>
              <Badge className="mt-2 bg-gradient-rose text-white capitalize">
                {selectedImage.category}
              </Badge>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}
