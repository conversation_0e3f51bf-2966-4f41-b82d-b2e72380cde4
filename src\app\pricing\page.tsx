import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  CheckCircle, 
  Star, 
  Clock,
  Heart,
  Sparkles,
  Crown,
  Palette,
  Scissors,
  Droplets,
  Hand
} from "lucide-react";

const pricingCategories = [
  {
    icon: Heart,
    title: "Bridal Services",
    color: "bg-rose-500",
    services: [
      { name: "Bridal Makeup (with trial)", price: "Rs. 15,000", duration: "3-4 hours", popular: true },
      { name: "Pre-Wedding Shoot Makeup", price: "Rs. 8,000", duration: "2-3 hours" },
      { name: "Bridal Hair Styling", price: "Rs. 5,000", duration: "2-3 hours" },
      { name: "Engagement Makeup", price: "Rs. 6,000", duration: "2 hours" },
      { name: "Reception Makeup", price: "Rs. 7,000", duration: "2-3 hours" },
    ],
  },
  {
    icon: Palette,
    title: "Makeup Services",
    color: "bg-purple-500",
    services: [
      { name: "Party Makeup", price: "Rs. 3,500", duration: "1.5-2 hours", popular: true },
      { name: "Natural Day Makeup", price: "Rs. 1,500", duration: "45 minutes" },
      { name: "Fashion/Editorial Makeup", price: "Rs. 6,000", duration: "2-3 hours" },
      { name: "Photoshoot Makeup", price: "Rs. 4,000", duration: "2 hours" },
      { name: "Special Event Makeup", price: "Rs. 2,500", duration: "1.5 hours" },
    ],
  },
  {
    icon: Scissors,
    title: "Hair Services",
    color: "bg-blue-500",
    services: [
      { name: "Hair Cut & Styling", price: "Rs. 1,200", duration: "1-2 hours" },
      { name: "Hair Coloring", price: "Rs. 4,500", duration: "2-4 hours", popular: true },
      { name: "Hair Treatment", price: "Rs. 2,500", duration: "1.5 hours" },
      { name: "Hair Straightening", price: "Rs. 8,000", duration: "3-4 hours" },
      { name: "Hair Curling/Perming", price: "Rs. 6,000", duration: "2-3 hours" },
    ],
  },
  {
    icon: Droplets,
    title: "Skincare Services",
    color: "bg-green-500",
    services: [
      { name: "Classic Facial", price: "Rs. 2,000", duration: "1 hour" },
      { name: "Anti-Aging Facial", price: "Rs. 3,500", duration: "1.5 hours", popular: true },
      { name: "Acne Treatment Facial", price: "Rs. 2,800", duration: "1.5 hours" },
      { name: "Hydrating Facial", price: "Rs. 2,200", duration: "1 hour" },
      { name: "Brightening Facial", price: "Rs. 3,000", duration: "1.5 hours" },
    ],
  },
  {
    icon: Hand,
    title: "Nail Services",
    color: "bg-pink-500",
    services: [
      { name: "Manicure & Pedicure", price: "Rs. 1,200", duration: "1.5 hours" },
      { name: "Nail Art", price: "Rs. 1,800", duration: "1-2 hours", popular: true },
      { name: "Gel Nail Extensions", price: "Rs. 2,500", duration: "2-3 hours" },
      { name: "Nail Polish Change", price: "Rs. 500", duration: "30 minutes" },
      { name: "French Manicure", price: "Rs. 800", duration: "1 hour" },
    ],
  },
];

const packages = [
  {
    name: "Bridal Deluxe",
    icon: Crown,
    price: "Rs. 25,000",
    originalPrice: "Rs. 30,000",
    duration: "Full Day",
    popular: true,
    description: "Complete bridal transformation package",
    features: [
      "Bridal makeup with trial session",
      "Bridal hair styling",
      "Pre-wedding shoot makeup",
      "Manicure & pedicure",
      "Facial treatment",
      "Touch-up kit included",
      "Professional consultation",
    ],
    color: "border-rose-500",
  },
  {
    name: "Party Glam",
    icon: Sparkles,
    price: "Rs. 8,000",
    originalPrice: "Rs. 10,000",
    duration: "4-5 hours",
    description: "Perfect for special events and parties",
    features: [
      "Party makeup",
      "Hair styling",
      "Manicure",
      "Basic facial",
      "Touch-up kit",
      "Style consultation",
    ],
    color: "border-purple-500",
  },
  {
    name: "Glow & Shine",
    icon: Droplets,
    price: "Rs. 6,500",
    originalPrice: "Rs. 8,000",
    duration: "3-4 hours",
    description: "Complete skincare and beauty treatment",
    features: [
      "Anti-aging facial",
      "Hair treatment",
      "Manicure & pedicure",
      "Natural day makeup",
      "Skincare consultation",
      "Home care tips",
    ],
    color: "border-green-500",
  },
];

export default function Pricing() {
  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <Hero
        title="Our Pricing"
        subtitle="Transparent & Affordable"
        description="Quality beauty services at competitive prices. No hidden costs, just exceptional value for your beauty needs."
        backgroundImage="https://images.unsplash.com/photo-1560066984-138dadb4c035?w=1920&h=800&fit=crop"
        primaryCTA="Book Service"
        secondaryCTA="View Packages"
      />

      {/* Service Pricing */}
      <Section>
        <SectionHeader
          subtitle="Service Pricing"
          title="Individual Service Rates"
          description="Choose from our comprehensive range of beauty services with transparent pricing."
        />
        
        <div className="space-y-12">
          {pricingCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-6">
              {/* Category Header */}
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 ${category.color} rounded-full flex items-center justify-center`}>
                  <category.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-playfair font-bold text-foreground">
                  {category.title}
                </h3>
              </div>
              
              {/* Services Table */}
              <Card>
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-muted/50">
                        <tr>
                          <th className="text-left p-4 font-semibold">Service</th>
                          <th className="text-left p-4 font-semibold">Duration</th>
                          <th className="text-left p-4 font-semibold">Price</th>
                          <th className="text-center p-4 font-semibold">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {category.services.map((service, serviceIndex) => (
                          <tr key={serviceIndex} className="border-t hover:bg-muted/30 transition-colors">
                            <td className="p-4">
                              <div className="flex items-center space-x-2">
                                <span className="font-medium">{service.name}</span>
                                {service.popular && (
                                  <Badge className="bg-gradient-rose text-white text-xs">
                                    Popular
                                  </Badge>
                                )}
                              </div>
                            </td>
                            <td className="p-4 text-muted-foreground">
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4" />
                                <span>{service.duration}</span>
                              </div>
                            </td>
                            <td className="p-4">
                              <span className="text-lg font-semibold text-primary">
                                {service.price}
                              </span>
                            </td>
                            <td className="p-4 text-center">
                              <Button size="sm" className="gradient-rose text-white">
                                Book Now
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </Section>

      {/* Package Pricing */}
      <Section background="gradient">
        <SectionHeader
          subtitle="Special Packages"
          title="Save More with Our Packages"
          description="Get the best value with our specially curated beauty packages designed for different occasions."
        />

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {packages.map((pkg, index) => (
            <Card
              key={index}
              className={`relative overflow-hidden hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 border-2 ${pkg.color} ${pkg.popular ? 'ring-2 ring-primary/20' : ''}`}
            >
              {pkg.popular && (
                <div className="absolute top-0 right-0 bg-gradient-rose text-white px-3 py-1 text-xs font-medium rounded-bl-lg">
                  Most Popular
                </div>
              )}

              <CardContent className="p-6 space-y-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto mb-4">
                    <pkg.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-playfair font-bold text-foreground">
                    {pkg.name}
                  </h3>
                  <p className="text-muted-foreground text-sm">{pkg.description}</p>
                </div>

                <div className="text-center space-y-1">
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <div className="text-sm text-muted-foreground line-through">
                    {pkg.originalPrice}
                  </div>
                  <div className="flex items-center justify-center space-x-1 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{pkg.duration}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold text-foreground">Package Includes:</h4>
                  <ul className="space-y-2">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start space-x-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <Button className="w-full gradient-rose text-white hover:opacity-90">
                  Book Package
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Additional Information */}
      <Section>
        <div className="grid lg:grid-cols-2 gap-12">
          <div className="space-y-6">
            <h3 className="text-2xl font-playfair font-bold text-foreground">
              Pricing Information
            </h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-foreground">Transparent Pricing</h4>
                  <p className="text-muted-foreground text-sm">
                    All prices are clearly listed with no hidden charges or surprise fees.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-foreground">Premium Products</h4>
                  <p className="text-muted-foreground text-sm">
                    We use only high-quality, branded products included in the service price.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-foreground">Professional Service</h4>
                  <p className="text-muted-foreground text-sm">
                    All services include consultation and professional expertise.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-foreground">Flexible Booking</h4>
                  <p className="text-muted-foreground text-sm">
                    Easy booking with advance payment options and flexible scheduling.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <h3 className="text-2xl font-playfair font-bold text-foreground">
              Payment & Booking
            </h3>
            <div className="space-y-4">
              <Card className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-primary font-bold text-sm">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Book Appointment</h4>
                    <p className="text-muted-foreground text-sm">
                      Call us or book online to schedule your service.
                    </p>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-primary font-bold text-sm">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Advance Payment</h4>
                    <p className="text-muted-foreground text-sm">
                      50% advance payment required to confirm booking.
                    </p>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-primary font-bold text-sm">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Service Day</h4>
                    <p className="text-muted-foreground text-sm">
                      Enjoy your service and pay the remaining amount.
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </Section>

      {/* CTA Section */}
      <Section background="muted">
        <div className="text-center space-y-6">
          <h2 className="text-3xl md:text-4xl font-playfair font-bold text-foreground">
            Ready to Book Your Service?
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Choose from our individual services or save more with our special packages.
            Contact us today to schedule your appointment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="gradient-rose text-white">
              Book Appointment
            </Button>
            <Button size="lg" variant="outline">
              Call: +977-9800000000
            </Button>
          </div>
        </div>
      </Section>

      <Footer />
    </div>
  );
}
