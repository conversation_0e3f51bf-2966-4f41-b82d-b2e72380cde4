import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Bella Beauty Studio | Premium Beauty Salon in Biratnagar",
  description: "Transform your beauty at Bella Beauty Studio in Biratnagar, Nepal. Expert bridal makeup, hair styling, facial treatments, and nail art services. Book your appointment today!",
  keywords: "beauty salon, makeup artist, bridal makeup, hair styling, facial, nail art, Biratnagar, Nepal",
  authors: [{ name: "Bella Beauty Studio" }],
  openGraph: {
    title: "Bella Beauty Studio | Premium Beauty Salon in Biratnagar",
    description: "Transform your beauty at Bella Beauty Studio. Expert makeup, hair styling, and beauty treatments in Biratnagar, Nepal.",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${playfair.variable} font-sans antialiased min-h-screen`}
      >
        {children}
      </body>
    </html>
  );
}
