"use client";

import { useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Send,
  CheckCircle,
  Facebook,
  Instagram,
  Twitter
} from "lucide-react";

const contactInfo = [
  {
    icon: MapPin,
    title: "Visit Our Studio",
    details: [
      "Main Road, Biratnagar",
      "Morang, Nepal",
      "Near City Center"
    ],
  },
  {
    icon: Phone,
    title: "Call Us",
    details: [
      "+977-9800000000",
      "+977-9811111111",
      "Available 9 AM - 7 PM"
    ],
  },
  {
    icon: Mail,
    title: "Email Us",
    details: [
      "<EMAIL>",
      "<EMAIL>",
      "We reply within 24 hours"
    ],
  },
  {
    icon: Clock,
    title: "Working Hours",
    details: [
      "Mon - Sat: 9:00 AM - 7:00 PM",
      "Sunday: 10:00 AM - 5:00 PM",
      "Closed on major holidays"
    ],
  },
];

const services = [
  "Bridal Makeup",
  "Party Makeup",
  "Hair Styling",
  "Hair Coloring",
  "Facial Treatment",
  "Nail Art",
  "Other"
];

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
    service: "",
    date: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: "",
        phone: "",
        email: "",
        service: "",
        date: "",
        message: ""
      });
    }, 3000);
  };

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <Hero
        title="Contact Us"
        subtitle="Get In Touch"
        description="Ready to transform your beauty? Contact us today to book your appointment or ask any questions about our services."
        backgroundImage="https://images.unsplash.com/photo-1560066984-138dadb4c035?w=1920&h=800&fit=crop"
        primaryCTA="Book Now"
        secondaryCTA="Call Us"
      />

      {/* Contact Information */}
      <Section>
        <SectionHeader
          subtitle="Contact Information"
          title="How to Reach Us"
          description="Multiple ways to get in touch with our team. We're here to help with all your beauty needs."
        />
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {contactInfo.map((info, index) => (
            <Card key={index} className="text-center p-6 hover:shadow-elegant transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto">
                  <info.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-playfair font-semibold text-foreground">
                  {info.title}
                </h3>
                <div className="space-y-1">
                  {info.details.map((detail, detailIndex) => (
                    <p key={detailIndex} className="text-muted-foreground text-sm">
                      {detail}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Contact Form & Map */}
      <Section background="gradient">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl font-playfair font-bold text-foreground mb-2">
                Book Your Appointment
              </h3>
              <p className="text-muted-foreground">
                Fill out the form below and we'll get back to you within 24 hours to confirm your appointment.
              </p>
            </div>
            
            {isSubmitted ? (
              <Card className="p-8 text-center bg-green-50 border-green-200">
                <CardContent className="space-y-4">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-green-800">
                    Message Sent Successfully!
                  </h3>
                  <p className="text-green-700">
                    Thank you for contacting us. We'll get back to you within 24 hours to confirm your appointment.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card className="p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Full Name *
                      </label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter your full name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Phone Number *
                      </label>
                      <Input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter your phone number"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Email Address
                    </label>
                    <Input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email address"
                    />
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Service Interested In
                      </label>
                      <select
                        name="service"
                        value={formData.service}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="">Select a service</option>
                        {services.map((service) => (
                          <option key={service} value={service}>
                            {service}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Preferred Date
                      </label>
                      <Input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Message
                    </label>
                    <Textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell us about your requirements or any special requests..."
                      rows={4}
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    className="w-full gradient-rose text-white"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      "Sending..."
                    ) : (
                      <>
                        Send Message
                        <Send className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </form>
              </Card>
            )}
          </div>

          {/* Map & Additional Info */}
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl font-playfair font-bold text-foreground mb-2">
                Find Our Studio
              </h3>
              <p className="text-muted-foreground">
                Located in the heart of Biratnagar, our studio is easily accessible and offers a comfortable, luxurious environment.
              </p>
            </div>
            
            {/* Map Placeholder */}
            <Card className="overflow-hidden">
              <div className="h-64 bg-muted flex items-center justify-center">
                <div className="text-center space-y-2">
                  <MapPin className="h-12 w-12 text-muted-foreground mx-auto" />
                  <p className="text-muted-foreground">Interactive Map</p>
                  <p className="text-sm text-muted-foreground">
                    Main Road, Biratnagar, Nepal
                  </p>
                </div>
              </div>
            </Card>
            
            {/* Social Media */}
            <Card className="p-6">
              <CardContent className="space-y-4">
                <h4 className="font-semibold text-foreground">Follow Us</h4>
                <p className="text-muted-foreground text-sm">
                  Stay updated with our latest work and beauty tips on social media.
                </p>
                <div className="flex space-x-3">
                  <Button size="icon" variant="outline" className="hover:bg-primary hover:text-primary-foreground">
                    <Facebook className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="outline" className="hover:bg-primary hover:text-primary-foreground">
                    <Instagram className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="outline" className="hover:bg-primary hover:text-primary-foreground">
                    <Twitter className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>

      {/* FAQ Section */}
      <Section>
        <SectionHeader
          subtitle="Frequently Asked Questions"
          title="Common Questions"
          description="Find answers to the most commonly asked questions about our services and booking process."
        />

        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-6">
            <Card className="p-6">
              <CardContent className="space-y-3">
                <h4 className="font-semibold text-foreground">How far in advance should I book?</h4>
                <p className="text-muted-foreground text-sm">
                  For bridal services, we recommend booking 2-3 months in advance. For regular services,
                  1-2 weeks is usually sufficient.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h4 className="font-semibold text-foreground">Do you provide trial sessions?</h4>
                <p className="text-muted-foreground text-sm">
                  Yes, we offer trial sessions for bridal makeup and special occasion services.
                  This helps ensure you're completely satisfied with the look.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h4 className="font-semibold text-foreground">What payment methods do you accept?</h4>
                <p className="text-muted-foreground text-sm">
                  We accept cash, bank transfers, and mobile payments. A 50% advance payment
                  is required to confirm your booking.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="p-6">
              <CardContent className="space-y-3">
                <h4 className="font-semibold text-foreground">Do you offer home services?</h4>
                <p className="text-muted-foreground text-sm">
                  Yes, we provide home services for bridal makeup and special occasions within
                  Biratnagar city limits. Additional charges may apply.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h4 className="font-semibold text-foreground">What products do you use?</h4>
                <p className="text-muted-foreground text-sm">
                  We use premium international brands like MAC, L'Oréal, Lakmé, and other
                  professional-grade cosmetics to ensure the best results.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h4 className="font-semibold text-foreground">Can I reschedule my appointment?</h4>
                <p className="text-muted-foreground text-sm">
                  Yes, you can reschedule with at least 24 hours notice. For bridal services,
                  we require 48 hours notice for rescheduling.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>

      {/* Emergency Contact */}
      <Section background="muted">
        <div className="text-center space-y-6">
          <h2 className="text-3xl md:text-4xl font-playfair font-bold text-foreground">
            Need Immediate Assistance?
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            For urgent bookings or last-minute appointments, call us directly.
            We'll do our best to accommodate your needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="gradient-rose text-white">
              <Phone className="mr-2 h-4 w-4" />
              Call Now: +977-9800000000
            </Button>
            <Button size="lg" variant="outline">
              <Mail className="mr-2 h-4 w-4" />
              Email: <EMAIL>
            </Button>
          </div>
        </div>
      </Section>

      <Footer />
    </div>
  );
}
