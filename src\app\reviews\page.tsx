import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import Section, { SectionHeader } from "@/components/Section";
import TestimonialCard from "@/components/TestimonialCard";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Star, 
  Users, 
  Award, 
  Heart,
  Quote,
  TrendingUp
} from "lucide-react";

const reviews = [
  {
    name: "<PERSON><PERSON>",
    role: "Bride",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Bella Beauty Studio made my wedding day absolutely perfect! The bridal makeup was flawless and lasted all day. <PERSON><PERSON> and her team are true artists. I felt like a princess!",
    service: "Bridal Makeup",
    date: "December 2023",
  },
  {
    name: "<PERSON>",
    role: "Regular Client",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "I've been coming here for over a year now, and the service is consistently excellent. The staff is professional, friendly, and always makes me feel pampered. Best salon in Biratnagar!",
    service: "Hair Styling",
    date: "January 2024",
  },
  {
    name: "Sunita Thapa",
    role: "Beauty Enthusiast",
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Their facial treatments are incredible! My skin has never looked better. The anti-aging facial really works, and the staff gives great skincare advice. Highly recommend!",
    service: "Facial Treatment",
    date: "November 2023",
  },
  {
    name: "Meera Gurung",
    role: "Party Goer",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Got my party makeup done here and received so many compliments! The makeup artist understood exactly what I wanted and delivered beyond my expectations.",
    service: "Party Makeup",
    date: "October 2023",
  },
  {
    name: "Kamala Shrestha",
    role: "Mother of Bride",
    image: "https://images.unsplash.com/photo-1551836022-d5d88e9218df?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Excellent service for my daughter's wedding. The entire bridal package was worth every penny. Professional, punctual, and the results were stunning!",
    service: "Bridal Package",
    date: "September 2023",
  },
  {
    name: "Rina Tamang",
    role: "Working Professional",
    image: "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=100&h=100&fit=crop&crop=face",
    rating: 4,
    testimonial: "Love their natural day makeup service. Perfect for office and daily wear. Quick service and the makeup looks fresh all day. Great value for money!",
    service: "Natural Day Makeup",
    date: "December 2023",
  },
  {
    name: "Sushma Adhikari",
    role: "Bride-to-be",
    image: "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Had my pre-wedding shoot makeup done here and the photos turned out amazing! The makeup was camera-perfect and the team was so professional.",
    service: "Pre-Wedding Makeup",
    date: "January 2024",
  },
  {
    name: "Laxmi Poudel",
    role: "Fashion Enthusiast",
    image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "Their hair coloring service is fantastic! Got highlights done and the color turned out exactly as I wanted. The hair feels healthy and looks gorgeous!",
    service: "Hair Coloring",
    date: "November 2023",
  },
  {
    name: "Gita Maharjan",
    role: "Skincare Lover",
    image: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    testimonial: "The acne treatment facial really helped clear my skin. The staff is knowledgeable about skincare and gave me a proper routine to follow at home.",
    service: "Acne Treatment",
    date: "October 2023",
  },
];

const stats = [
  {
    icon: Users,
    number: "1000+",
    label: "Happy Clients",
    description: "Satisfied customers served",
  },
  {
    icon: Star,
    number: "4.9",
    label: "Average Rating",
    description: "Based on client reviews",
  },
  {
    icon: Award,
    number: "500+",
    label: "Bridal Makeups",
    description: "Beautiful brides transformed",
  },
  {
    icon: TrendingUp,
    number: "95%",
    label: "Return Rate",
    description: "Clients who return for more services",
  },
];

const ratingBreakdown = [
  { stars: 5, count: 847, percentage: 94 },
  { stars: 4, count: 45, percentage: 5 },
  { stars: 3, count: 8, percentage: 1 },
  { stars: 2, count: 0, percentage: 0 },
  { stars: 1, count: 0, percentage: 0 },
];

export default function Reviews() {
  const totalReviews = ratingBreakdown.reduce((sum, rating) => sum + rating.count, 0);
  const averageRating = (
    ratingBreakdown.reduce((sum, rating) => sum + rating.stars * rating.count, 0) / totalReviews
  ).toFixed(1);

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <Hero
        title="Client Reviews"
        subtitle="What Our Clients Say"
        description="Read authentic reviews from our satisfied clients and discover why Bella Beauty Studio is Biratnagar's most trusted beauty destination."
        backgroundImage="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=1920&h=800&fit=crop"
        primaryCTA="Book Service"
        secondaryCTA="View Gallery"
      />

      {/* Statistics */}
      <Section>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center p-6 hover:shadow-elegant transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-gradient-rose rounded-full flex items-center justify-center mx-auto">
                  <stat.icon className="h-8 w-8 text-white" />
                </div>
                <div>
                  <div className="text-3xl font-bold text-primary">{stat.number}</div>
                  <div className="font-semibold text-foreground">{stat.label}</div>
                  <div className="text-sm text-muted-foreground">{stat.description}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Rating Overview */}
      <Section background="gradient">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <div className="text-center lg:text-left">
              <div className="text-6xl font-bold text-primary mb-2">{averageRating}</div>
              <div className="flex items-center justify-center lg:justify-start space-x-1 mb-2">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-6 w-6 ${
                      i < Math.floor(parseFloat(averageRating))
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <div className="text-muted-foreground">
                Based on {totalReviews} reviews
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            {ratingBreakdown.map((rating) => (
              <div key={rating.stars} className="flex items-center space-x-4">
                <div className="flex items-center space-x-1 w-16">
                  <span className="text-sm font-medium">{rating.stars}</span>
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                </div>
                <div className="flex-1 bg-muted rounded-full h-2">
                  <div
                    className="bg-gradient-rose h-2 rounded-full transition-all duration-300"
                    style={{ width: `${rating.percentage}%` }}
                  />
                </div>
                <div className="text-sm text-muted-foreground w-12 text-right">
                  {rating.count}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Section>

      {/* Client Reviews */}
      <Section>
        <SectionHeader
          subtitle="Client Testimonials"
          title="Real Reviews from Real Clients"
          description="Read what our clients have to say about their experience at Bella Beauty Studio."
        />
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reviews.map((review, index) => (
            <div key={index} className="relative">
              <TestimonialCard {...review} />
              <div className="absolute top-3 right-3">
                <Badge variant="secondary" className="text-xs">
                  {review.date}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </Section>
